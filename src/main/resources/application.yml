# Spring Boot Configuration
spring:
  application:
    name: photo-upload-system
  
  # Database Configuration
  datasource:
    url: jdbc:h2:file:./data/photodb
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
  
  # H2 Console (for development)
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # File Upload Configuration
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
      enabled: true
  
  # Security Configuration
  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN
  
  # Cache Configuration
  cache:
    type: simple

# Server Configuration
server:
  port: 8080
  servlet:
    context-path: /api
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

# Logging Configuration
logging:
  level:
    com.photoapp: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web.multipart: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/photo-app.log

# Custom Application Properties
photo-app:
  upload:
    # 文件存储路径
    path: ./uploads/photos
    # 缩略图存储路径
    thumbnail-path: ./uploads/thumbnails
    # 允许的文件类型
    allowed-types: jpg,jpeg,png,gif,bmp,webp
    # 最大文件大小 (10MB)
    max-file-size: 10485760
    # 缩略图尺寸
    thumbnail:
      width: 200
      height: 200
      quality: 0.8
    # 图片压缩质量
    compression:
      quality: 0.9
      max-width: 1920
      max-height: 1080
  
  security:
    # JWT配置 (如果需要)
    jwt:
      secret: mySecretKey
      expiration: 86400000 # 24 hours
    # 防盗链配置
    referer:
      enabled: true
      allowed-domains: localhost,127.0.0.1
    # 访问控制
    access:
      rate-limit: 100 # 每分钟最大请求数
      
  storage:
    # 存储清理配置
    cleanup:
      enabled: true
      # 清理间隔 (小时)
      interval: 24
      # 文件保留天数
      retention-days: 30
    # 存储监控
    monitoring:
      enabled: true
      # 最大存储空间 (GB)
      max-storage-gb: 10

# API Documentation
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  packages-to-scan: com.photoapp.controller

# Management Endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
